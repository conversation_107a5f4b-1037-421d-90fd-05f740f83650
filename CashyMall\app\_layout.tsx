import { Stack } from "expo-router";
import { useColorScheme, ActivityIndicator, View, Platform } from "react-native";
import { useFonts } from "expo-font";
import { StatusBar } from "expo-status-bar";
import { ThemeProvider, DefaultTheme, DarkTheme } from "@react-navigation/native";
import { AuthProvider, useAuth } from "../context/AuthContext";
import Colors from "../constants/Colors";
import * as SplashScreen from 'expo-splash-screen';
import { useCallback, useEffect, useState } from "react";
import AnimatedSplashScreen from "../components/AnimatedSplashScreen";
import {SafeAreaView, SafeAreaProvider} from 'react-native-safe-area-context';
import {AlertNotificationRoot } from 'react-native-alert-notification';
import { StyleSheet } from "react-native";


const styles = StyleSheet.create({
  container: {
    paddingTop: Platform.OS === 'android' ? 25 : 0,
  },
});

// Keep the splash screen visible while we fetch resources
SplashScreen.preventAutoHideAsync();

// Root layout wrapper with auth provider
export default function RootLayoutWrapper() {
  
  return (
    <SafeAreaProvider>
     
       {/* <AlertNotificationRoot> */}
    <AuthProvider>
      <RootLayout />
    </AuthProvider>
   {/* </AlertNotificationRoot> */}
    </SafeAreaProvider>
  );
}

// Main root layout
function RootLayout() {
  const colorScheme = useColorScheme() || 'light';
  const colors = Colors[colorScheme];
  const { isLoading } = useAuth();
  const [showAnimatedSplash, setShowAnimatedSplash] = useState(true);

  const [loaded] = useFonts({
    // We can add custom fonts here later if needed
  });

  // Handle hiding the splash screen
  const onLayoutRootView = useCallback(async () => {
    if (loaded) {
      // Hide the native splash screen
      await SplashScreen.hideAsync();
    }
  }, [loaded]);

  // Effect to handle the splash screen
  useEffect(() => {
    if (loaded) {
      onLayoutRootView();
    }
  }, [loaded, onLayoutRootView]);

  // Handle when animated splash screen completes
  const handleAnimationComplete = () => {
    setShowAnimatedSplash(false);
  };

  if (!loaded) {
    return null;
  }

  // Show animated splash screen
  if (showAnimatedSplash) {
    return <AnimatedSplashScreen onAnimationComplete={handleAnimationComplete} />;
  }

  // Show loading indicator while checking authentication
  if (isLoading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: colors.background }}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    );
  }

  return (
    <ThemeProvider value={colorScheme === "dark" ? DarkTheme : DefaultTheme}>
      <Stack screenOptions={{ headerShown: false ,contentStyle: styles.container }}  >
        <Stack.Screen name="(tabs)" />
        <Stack.Screen name="auth/login" options={{ headerShown: false, title: "Login" }} />
        <Stack.Screen name="auth/signup" options={{ headerShown: false, title: "Sign Up" }} />
        <Stack.Screen name="auth/forgot-password" options={{ headerShown: false, title: "Forgot Password" }} />
        <Stack.Screen name="auth/reset-password" options={{ headerShown: false, title: "Reset Password" }} />
        <Stack.Screen name="product/[id]" options={{ headerShown: true, title: "Product Details" }} />
        <Stack.Screen name="cart/index" options={{ headerShown: false, title: "Shopping Cart" }} />
        <Stack.Screen name="cart/checkout" options={{ headerShown: true, title: "Checkout" }} />
        <Stack.Screen name="profile/edit" options={{ headerShown: false, title: "Edit Profile" }} />
        <Stack.Screen name="profile/orders" options={{ headerShown: false, title: "My Orders" }} />
        <Stack.Screen name="profile/order-details" options={{ headerShown: false, title: "Order Details" }} />
        <Stack.Screen name="profile/wishlist" options={{ headerShown: false, title: "Wishlist" }} />
        <Stack.Screen name="profile/addresses" options={{ headerShown: false, title: "Shipping Addresses" }} />
        <Stack.Screen name="profile/settings" options={{ headerShown: false, title: "Settings" }} />
        <Stack.Screen name="profile/support" options={{ headerShown: false, title: "Help & Support" }} />
        <Stack.Screen name="search" options={{ headerShown: false, title: "Search" }} />
        <Stack.Screen name="index" />
      </Stack>
      <StatusBar style={colorScheme === "dark" ? "light" : "dark"} />
    </ThemeProvider>
  );
}
