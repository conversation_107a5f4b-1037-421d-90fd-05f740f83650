import React, { useContext, useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  useColorScheme,
  Image,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Alert
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import Input from '../../components/Input';
import Button from '../../components/Button';
import Colors from '../../constants/Colors';
import Layout from '../../constants/Layout';
import { AuthContext } from '@/context/AuthContext';

// Mock user data


export default function EditProfileScreen() {
      const { user, isSignedIn, signOut } = useContext(AuthContext)!;
  
const userData = {
  name: `${user?.firstname} ${user?.lastname}`,
  firstName: user?.firstname || '',
  lastName: user?.lastname || '',
  email: user?.email || '',
  phone: user?.phoneNumber || '',
  avatar: user?.profilePicture || '',
};

  const [firstName, setFirstName] = useState(userData.firstName);
  const [lastName, setLastName] = useState(userData.lastName);
  const [email, setEmail] = useState(userData.email);
  const [phone, setPhone] = useState(userData.phone);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<{ 
    firstName?: string; 
    lastName?: string;
    email?: string; 
    phone?: string;
  }>({});


  const colorScheme = useColorScheme() || 'light';
  const colors = Colors[colorScheme];
  const router = useRouter();

  const validateForm = () => {
    const newErrors: typeof errors = {};
    let isValid = true;

    // Validate first name
    if (!firstName.trim()) {
      newErrors.firstName = 'First name is required';
      isValid = false;
    }
    
    // Validate last name
    if (!lastName.trim()) {
      newErrors.lastName = 'Last name is required';
      isValid = false;
    }
    
    // Validate email
    if (!email.trim()) {
      newErrors.email = 'Email is required';
      isValid = false;
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = 'Email is invalid';
      isValid = false;
    }
    
    // Validate phone
    if (!phone.trim()) {
      newErrors.phone = 'Phone number is required';
      isValid = false;
    }
    
    setErrors(newErrors);
    return isValid;
  };

  const handleUpdateProfile = async () => {
    if (!validateForm()) {
      return;
    }
    
    setIsLoading(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // In a real app, you would update the user profile in your backend
      // const response = await api.updateProfile({
      //   firstName,
      //   lastName,
      //   email,
      //   phone
      // });
      
      Alert.alert(
        'Profile Updated',
        'Your profile has been updated successfully.',
        [
          { 
            text: 'OK', 
            onPress: () => router.back() 
          }
        ]
      );
    } catch (error) {
      console.error('Profile update error:', error);
      Alert.alert(
        'Error',
        'There was an error updating your profile. Please try again.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleChangeAvatar = () => {
    Alert.alert(
      'Change Profile Picture',
      'Choose an option',
      [
        {
          text: 'Take Photo',
          onPress: () => console.log('Take Photo pressed'),
        },
        {
          text: 'Choose from Gallery',
          onPress: () => console.log('Choose from Gallery pressed'),
        },
        {
          text: 'Cancel',
          style: 'cancel',
        },
      ]
    );
  };

  return (
    <KeyboardAvoidingView
      style={[styles.container, { backgroundColor: colors.background }]}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
    >
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name="chevron-back" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>Edit Profile</Text>
        <View style={styles.placeholder} />
      </View>
      
      <ScrollView 
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.avatarContainer}>
          <Image 
            source={{ uri: userData.avatar }} 
            style={styles.avatar}
          />
          <TouchableOpacity 
            style={[styles.editAvatarButton, { backgroundColor: colors.primary }]}
            onPress={handleChangeAvatar}
          >
            <Ionicons name="camera" size={16} color="#ffffff" />
          </TouchableOpacity>
        </View>
        
        <View style={styles.formContainer}>
          <Input
            label="First Name"
            placeholder="Enter your first name"
            autoCapitalize="words"
            value={firstName}
            onChangeText={setFirstName}
            error={errors.firstName}
            leftIcon={<Ionicons name="person-outline" size={20} color={colors.tabIconDefault} />}
          />
          
          <Input
            label="Last Name"
            placeholder="Enter your last name"
            autoCapitalize="words"
            value={lastName}
            onChangeText={setLastName}
            error={errors.lastName}
            leftIcon={<Ionicons name="person-outline" size={20} color={colors.tabIconDefault} />}
          />
          
          <Input
            label="Email Address"
            placeholder="Enter your email"
            keyboardType="email-address"
            autoCapitalize="none"
            value={email}
            onChangeText={setEmail}
            error={errors.email}
            leftIcon={<Ionicons name="mail-outline" size={20} color={colors.tabIconDefault} />}
          />
          
          <Input
            label="Phone Number"
            placeholder="Enter your phone number"
            keyboardType="phone-pad"
            value={phone}
            onChangeText={setPhone}
            error={errors.phone}
            leftIcon={<Ionicons name="call-outline" size={20} color={colors.tabIconDefault} />}
          />
          
          <Button
            title="Update Profile"
            onPress={handleUpdateProfile}
            isLoading={isLoading}
            fullWidth
            style={styles.updateButton}
          />
          
          <TouchableOpacity 
            style={styles.changePasswordButton}
            onPress={() => router.push('/auth/reset-password')}
          >
            <Text style={[styles.changePasswordText, { color: colors.primary }]}>
              Change Password
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Layout.spacing.md,
    paddingTop: Layout.spacing.lg,
    paddingBottom: Layout.spacing.md,
  },
  backButton: {
    padding: Layout.spacing.xs,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  placeholder: {
    width: 32,
  },
  scrollView: {
    flex: 1,
  },
  avatarContainer: {
    alignItems: 'center',
    marginVertical: Layout.spacing.lg,
    position: 'relative',
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  editAvatarButton: {
    position: 'absolute',
    bottom: 0,
    right: '35%',
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  formContainer: {
    paddingHorizontal: Layout.spacing.md,
    marginBottom: Layout.spacing.xl,
  },
  updateButton: {
    marginTop: Layout.spacing.md,
  },
  changePasswordButton: {
    alignSelf: 'center',
    marginTop: Layout.spacing.lg,
    padding: Layout.spacing.sm,
  },
  changePasswordText: {
    fontSize: 16,
    fontWeight: '500',
  },
});
