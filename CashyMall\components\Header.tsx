import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  useColorScheme,
  Image
} from 'react-native';
import { useRouter } from 'expo-router';
import { useFocusEffect } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import Colors from '../constants/Colors';
import Layout from '../constants/Layout';
import { getWishlists } from '../services/api/wishlist';
import { getCart } from '../services/api/cart';

interface HeaderProps {
  title?: string;
  showBack?: boolean;
  showCart?: boolean;
  showWishlist?: boolean;
  showSearch?: boolean;
  showLogo?: boolean;
}

export default function Header({
  title,
  showBack = false,
  showCart = true,
  showWishlist = true,
  showSearch = true,
  showLogo = false,
}: HeaderProps) {
  const colorScheme = useColorScheme() || 'light';
  const colors = Colors[colorScheme];
  const router = useRouter();

  // State for wishlist count
  const [wishlistCount, setWishlistCount] = useState<number>(0);
  const [wishlistLoading, setWishlistLoading] = useState<boolean>(false);

  // State for cart count
  const [cartCount, setCartCount] = useState<number>(0);
  const [cartLoading, setCartLoading] = useState<boolean>(false);

  // Function to fetch wishlist count
  const fetchWishlistCount = useCallback(async () => {
    if (!showWishlist) return; // Don't fetch if wishlist icon is not shown

    try {
      setWishlistLoading(true);
      const response = await getWishlists();
      console.log('Header: Wishlist Response:', response);

      if (response && response.data && response.data.products) {
        const count = response.data.products.length;
        setWishlistCount(count);
        console.log('Header: Wishlist count updated to:', count);
      } else {
        setWishlistCount(0);
      }
    } catch (error) {
      console.error('Header: Error fetching wishlist count:', error);
      setWishlistCount(0);
    } finally {
      setWishlistLoading(false);
    }
  }, [showWishlist]);

  // Function to fetch cart count
  const fetchCartCount = useCallback(async () => {
    if (!showCart) return; // Don't fetch if cart icon is not shown

    try {
      setCartLoading(true);
      const response = await getCart();
      console.log('Header: Cart Response:', response);

      if (response && response.data && typeof response.data.itemCount === 'number') {
        setCartCount(response.data.itemCount);
        console.log('Header: Cart count updated to:', response.data.itemCount);
      } else if (response && response.data && Array.isArray(response.data.items)) {
        // Fallback: count items manually if itemCount is not available
        const count = response.data.items.length;
        setCartCount(count);
        console.log('Header: Cart count (fallback) updated to:', count);
      } else {
        setCartCount(0);
      }
    } catch (error) {
      console.error('Header: Error fetching cart count:', error);
      setCartCount(0);
    } finally {
      setCartLoading(false);
    }
  }, [showCart]);

  // Fetch counts on mount
  useEffect(() => {
    fetchWishlistCount();
    fetchCartCount();
  }, [fetchWishlistCount, fetchCartCount]);

  // Refresh counts when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      fetchWishlistCount();
      fetchCartCount();
    }, [fetchWishlistCount, fetchCartCount])
  );

  // Create style objects with dynamic properties to avoid array styles for web
  const containerStyle = {
    ...styles.container,
    backgroundColor: colors.background
  };

  const titleStyle = {
    ...styles.title,
    color: colors.text
  };

  const badgeStyle = {
    ...styles.badge,
    backgroundColor: colors.primary
  };

  return (
    <View style={containerStyle}>
      <View style={styles.leftContainer}>
        {showBack && (
          <TouchableOpacity
            style={styles.iconButton}
            onPress={() => {
              try {
                router.back();
              } catch (error) {
                // If there's no screen to go back to, navigate to home
                console.log('No screen to go back to, navigating to home');
                router.push('/(tabs)');
              }
            }}
          >
            <Ionicons
              name="chevron-back"
              size={24}
              color={colors.text}
            />
          </TouchableOpacity>
        )}

        {showLogo && (
          <Image
            source={require('../assets/images/logo.png')}
            style={styles.logo}
            resizeMode="contain"
          />
        )}

        {title && (
          <Text style={titleStyle}>
            {title}
          </Text>
        )}
      </View>

      <View style={styles.rightContainer}>
        {showSearch && (
          <TouchableOpacity
            style={styles.iconButton}
            onPress={() => router.push('/search')}
          >
            <Ionicons
              name="search-outline"
              size={22}
              color={colors.text}
            />
          </TouchableOpacity>
        )}

        {showWishlist && (
          <TouchableOpacity
            style={styles.iconButton}
            onPress={() => router.push('/profile/wishlist')}
          >
            <View style={styles.cartContainer}>
              <Ionicons
                name="heart-outline"
                size={22}
                color={colors.text}
              />
              {wishlistCount > 0 && (
                <View style={badgeStyle}>
                  <Text style={styles.badgeText}>{wishlistCount}</Text>
                </View>
              )}
            </View>
          </TouchableOpacity>
        )}

        {showCart && (
          <TouchableOpacity
            style={styles.iconButton}
            onPress={() => router.push('/cart')}
          >
            <View style={styles.cartContainer}>
              <Ionicons
                name="cart-outline"
                size={22}
                color={colors.text}
              />
              {cartCount > 0 && (
                <View style={badgeStyle}>
                  <Text style={styles.badgeText}>{cartCount}</Text>
                </View>
              )}
            </View>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Layout.spacing.md,
    paddingVertical: Layout.spacing.sm,
    height: 60,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.05)',
  },
  leftContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rightContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
  },
  logo: {
    width: 120,
    height: 30,
  },
  iconButton: {
    padding: Layout.spacing.xs,
    marginLeft: Layout.spacing.sm,
  },
  cartContainer: {
    position: 'relative',
  },
  badge: {
    position: 'absolute',
    top: -8,
    right: -8,
    minWidth: 16,
    height: 16,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 4,
  },
  badgeText: {
    color: '#ffffff',
    fontSize: 10,
    fontWeight: 'bold',
  },
});
