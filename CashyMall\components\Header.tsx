import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  useColorScheme,
  Image
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import Colors from '../constants/Colors';
import Layout from '../constants/Layout';

interface HeaderProps {
  title?: string;
  showBack?: boolean;
  showCart?: boolean;
  showSearch?: boolean;
  showLogo?: boolean;
}

export default function Header({
  title,
  showBack = false,
  showCart = true,
  showSearch = true,
  showLogo = false,
}: HeaderProps) {
  const colorScheme = useColorScheme() || 'light';
  const colors = Colors[colorScheme];
  const router = useRouter();

  // Create style objects with dynamic properties to avoid array styles for web
  const containerStyle = {
    ...styles.container,
    backgroundColor: colors.background
  };

  const titleStyle = {
    ...styles.title,
    color: colors.text
  };

  const badgeStyle = {
    ...styles.badge,
    backgroundColor: colors.primary
  };

  return (
    <View style={containerStyle}>
      <View style={styles.leftContainer}>
        {showBack && (
          <TouchableOpacity
            style={styles.iconButton}
            onPress={() => {
              try {
                router.back();
              } catch (error) {
                // If there's no screen to go back to, navigate to home
                console.log('No screen to go back to, navigating to home');
                router.push('/(tabs)');
              }
            }}
          >
            <Ionicons
              name="chevron-back"
              size={24}
              color={colors.text}
            />
          </TouchableOpacity>
        )}

        {showLogo && (
          <Image
            source={require('../assets/images/logo.png')}
            style={styles.logo}
            resizeMode="contain"
          />
        )}

        {title && (
          <Text style={titleStyle}>
            {title}
          </Text>
        )}
      </View>

      <View style={styles.rightContainer}>
        {showSearch && (
          <TouchableOpacity
            style={styles.iconButton}
            onPress={() => router.push('/search')}
          >
            <Ionicons
              name="search-outline"
              size={22}
              color={colors.text}
            />
          </TouchableOpacity>
        )}

        {showCart && (
          <TouchableOpacity
            style={styles.iconButton}
            onPress={() => router.push('/cart')}
          >
            <View style={styles.cartContainer}>
              <Ionicons
                name="cart-outline"
                size={22}
                color={colors.text}
              />
              <View style={badgeStyle}>
                <Text style={styles.badgeText}>2</Text>
              </View>
            </View>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Layout.spacing.md,
    paddingVertical: Layout.spacing.sm,
    height: 60,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.05)',
  },
  leftContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rightContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
  },
  logo: {
    width: 120,
    height: 30,
  },
  iconButton: {
    padding: Layout.spacing.xs,
    marginLeft: Layout.spacing.sm,
  },
  cartContainer: {
    position: 'relative',
  },
  badge: {
    position: 'absolute',
    top: -8,
    right: -8,
    minWidth: 16,
    height: 16,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 4,
  },
  badgeText: {
    color: '#ffffff',
    fontSize: 10,
    fontWeight: 'bold',
  },
});
